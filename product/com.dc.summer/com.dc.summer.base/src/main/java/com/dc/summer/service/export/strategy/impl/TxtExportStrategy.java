package com.dc.summer.service.export.strategy.impl;

import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.export.strategy.AbstractExportStrategy;
import com.dc.summer.service.sql.WebSQLTransferResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 文本导出策略实现
 * 处理TXT格式的导出
 */
@Slf4j
@Component
public class TxtExportStrategy extends AbstractExportStrategy {

    private static final List<ExportType> SUPPORTED_TYPES = Arrays.asList(
            ExportType.TXT
    );

    @Override
    protected WebSQLTransferResult doExport(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.info("执行文本导出策略");
        
        // 记录导出信息
        logExportInfo(context, resultSets);
        
        // 应用文本特定的配置
        applyTextConfiguration(context);
        
        // 执行导出
        return executeTransfer(context, resultSets);
    }

    @Override
    public boolean supports(ExportType exportType) {
        return SUPPORTED_TYPES.contains(exportType);
    }

    @Override
    public String getStrategyName() {
        return "文本导出策略";
    }

    @Override
    public List<ExportType> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public boolean validateParameters(ExportContext context) {
        if (!super.validateParameters(context)) {
            return false;
        }
        
        // 文本特定的参数验证
        if (context.getFormatConfig().getColumnDelimiter() == null && 
            context.getFormatConfig().getOtherDelimiter() == null) {
            log.error("文本导出必须指定列分隔符");
            return false;
        }
        
        return true;
    }

    @Override
    public List<WebSQLQueryResultSet> preprocessData(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.debug("文本导出数据预处理");
        
        // 文本特定的数据预处理
        processTextFormatting(context, resultSets);
        
        return resultSets;
    }

    /**
     * 应用文本配置
     */
    private void applyTextConfiguration(ExportContext context) {
        log.info("应用文本配置 - 列分隔符: {}, 行分隔符: {}, 编码: {}", 
                context.getFormatConfig().getColumnDelimiter(),
                context.getFormatConfig().getLineDelimiter(),
                context.getFormatConfig().getFileCharset());
    }

    /**
     * 处理文本格式化
     */
    private void processTextFormatting(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.debug("处理文本格式化");
        
        // 处理特殊字符
        // 处理换行符
        // 处理编码转换等
        
        // 这里可以添加具体的文本格式化逻辑
    }
}
