package com.dc.summer.service.export.strategy;

import com.dc.springboot.core.model.type.ExportType;
import com.dc.springboot.core.model.result.WebSQLQueryResultSet;
import com.dc.summer.model.export.context.ExportContext;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.service.sql.WebSQLTransferResult;
import com.dc.summer.transfer.DataTransferProcessorDescriptor;
import com.dc.summer.transfer.DataTransferRegistry;
import com.dc.summer.transfer.WebDataTransferName;
import com.dc.summer.transfer.chain.ChainBuilder;
import com.dc.summer.transfer.chain.StreamChainRunner;
import com.dc.summer.transfer.chain.ToastChain;
import com.dc.summer.model.type.OriginType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 抽象导出策略基类
 * 提供通用的导出逻辑实现
 */
@Slf4j
public abstract class AbstractExportStrategy implements ExportStrategy {

    /**
     * 执行导出的通用模板方法
     */
    @Override
    public WebSQLTransferResult export(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.info("开始执行导出策略: {}, 导出类型: {}", getStrategyName(), context.getFormatConfig().getExportType());
        
        // 1. 验证参数
        if (!validateParameters(context)) {
            throw new IllegalArgumentException("导出参数验证失败");
        }
        
        // 2. 预处理数据
        List<WebSQLQueryResultSet> processedResultSets = preprocessData(context, resultSets);
        
        // 3. 执行具体的导出逻辑
        WebSQLTransferResult result = doExport(context, processedResultSets);
        
        // 4. 后处理结果
        result = postprocessResult(context, result);
        
        log.info("导出策略执行完成: {}", getStrategyName());
        return result;
    }

    /**
     * 具体的导出实现，由子类实现
     */
    protected abstract WebSQLTransferResult doExport(ExportContext context, List<WebSQLQueryResultSet> resultSets);

    /**
     * 通用的导出逻辑实现
     */
    protected WebSQLTransferResult executeTransfer(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(context.getToken());
        
        // 获取数据传输处理器
        DataTransferProcessorDescriptor processor = DataTransferRegistry.getInstance()
                .getProcessor(context.getFormatConfig().getExportType().getProcessorFullId());
        
        // 构建文件名
        WebDataTransferName fileName = buildFileName(context);
        
        // 执行数据传输
        return contextInfo.getTransfer().exportDataByContext(
                null, // monitor 在异步任务中会被设置
                processor,
                () -> resultSets,
                context, // 传递整个上下文而不是原始的message
                context.getUserId(),
                resultSets1 -> ChainBuilder.build(new StreamChainRunner<WebSQLTransferResult>())
                        .addChain(new ToastChain(resultSets1)),
                new WebDataTransferName.ResultSetName(),
                fileName,
                true,
                true,
                false,
                context.getFormatConfig().getExportFileName(),
                context.getTokenConfig(),
                OriginType.BROWSER
        );
    }

    /**
     * 构建文件名
     */
    protected WebDataTransferName buildFileName(ExportContext context) {
        String customFileName = context.getFormatConfig().getExportFileName();
        if (StringUtils.isNotBlank(customFileName)) {
            return new WebDataTransferName.CustomResultSetName(customFileName);
        } else {
            return new WebDataTransferName.ResultSetName();
        }
    }

    /**
     * 验证导出类型是否匹配
     */
    protected boolean isExportTypeMatched(ExportContext context) {
        return supports(context.getFormatConfig().getExportType());
    }

    /**
     * 记录导出日志
     */
    protected void logExportInfo(ExportContext context, List<WebSQLQueryResultSet> resultSets) {
        log.info("执行导出 - 策略: {}, 类型: {}, 用户: {}, 结果集数量: {}", 
                getStrategyName(), 
                context.getFormatConfig().getExportType(),
                context.getUserId(),
                resultSets != null ? resultSets.size() : 0);
    }
}
